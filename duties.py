from flask import Blueprint, render_template, request, jsonify, send_file
from flask_login import login_required, current_user
from io import BytesIO
from datetime import datetime
import json

from db import db
from models import DutiesExcelData

# محاولة استيراد مكتبات Excel
try:
    import pandas as pd
    EXCEL_AVAILABLE = True
    print("✅ pandas متوفرة")
except ImportError as e:
    EXCEL_AVAILABLE = False
    print(f"❌ pandas غير متوفرة: {e}")

try:
    import openpyxl  # noqa: F401  # للتأكد من توفر محرك القراءة
    OPENPYXL_AVAILABLE = True
    print("✅ openpyxl متوفرة")
except ImportError as e:
    OPENPYXL_AVAILABLE = False
    print(f"❌ openpyxl غير متوفرة: {e}")

try:
    import xlsxwriter  # للكتابة كخيار بديل
    XLSXWRITER_AVAILABLE = True
    print("✅ xlsxwriter متوفرة")
except ImportError as e:
    XLSXWRITER_AVAILABLE = False
    print(f"❌ xlsxwriter غير متوفرة: {e}")

print(f"📊 حالة المكتبات: pandas={EXCEL_AVAILABLE}, openpyxl={OPENPYXL_AVAILABLE}, xlsxwriter={XLSXWRITER_AVAILABLE}")


duties_bp = Blueprint('duties', __name__, url_prefix='/duties')


@duties_bp.route('/')
@login_required
def index():
    return render_template('duties/index.html')


@duties_bp.route('/api/data', methods=['GET'])
@login_required
def get_data():
    """جلب أحدث بيانات كشف الواجبات"""
    print(f"📥 طلب جلب البيانات من المستخدم: {current_user.username}")
    record = DutiesExcelData.query.order_by(DutiesExcelData.updated_at.desc()).first()
    if not record:
        print("📭 لا توجد بيانات محفوظة")
        return jsonify({
            'success': True,
            'data': [],
            'columns': [],
            'message': 'لا توجد بيانات حالياً، يرجى رفع ملف إكسل.'
        })

    columns = []
    if record.columns_json:
        try:
            columns = json.loads(record.columns_json)
        except Exception:
            columns = []

    try:
        data = json.loads(record.data_json)
    except Exception:
        data = []

    return jsonify({'success': True, 'data': data, 'columns': columns, 'filename': record.filename})


@duties_bp.route('/api/upload', methods=['POST'])
@login_required
def upload_excel():
    """رفع ملف إكسل وحفظه كبيانات في قاعدة البيانات"""
    print(f"📥 تم استلام طلب رفع ملف من المستخدم: {current_user.username}")

    if not EXCEL_AVAILABLE:
        print("❌ pandas غير متوفرة")
        return jsonify({'success': False, 'message': 'حزمة pandas غير متوفرة على الخادم.'}), 400
    if not OPENPYXL_AVAILABLE:
        print("❌ openpyxl غير متوفرة")
        return jsonify({'success': False, 'message': 'حزمة openpyxl غير متوفرة على الخادم.'}), 400

    print(f"📋 الملفات في الطلب: {list(request.files.keys())}")
    if 'file' not in request.files:
        print("❌ لا يوجد ملف في الطلب")
        return jsonify({'success': False, 'message': 'لم يتم اختيار ملف.'}), 400

    f = request.files['file']
    print(f"📄 اسم الملف: {f.filename}")
    if f.filename == '':
        print("❌ اسم الملف فارغ")
        return jsonify({'success': False, 'message': 'لم يتم اختيار ملف.'}), 400

    try:
        print(f"📊 بدء قراءة ملف Excel: {f.filename}")
        # قراءة ملف الإكسل (أول ورقة فقط حالياً)
        df = pd.read_excel(f, engine='openpyxl')
        print(f"📈 تم قراءة {len(df)} صف و {len(df.columns)} عمود")

        # استبدال القيم NaN بسلاسل فارغة لتبسيط العرض
        df = df.fillna('')

        # توليد بيانات للأعمدة والصفوف
        columns = list(map(str, df.columns.tolist()))
        data = df.to_dict(orient='records')
        print(f"📋 الأعمدة: {columns}")

        # حذف أي بيانات سابقة (ملف واحد نشط فقط)
        deleted_count = DutiesExcelData.query.count()
        DutiesExcelData.query.delete()
        print(f"🗑️ تم حذف {deleted_count} سجل سابق")

        record = DutiesExcelData(
            filename=f.filename,
            sheet_name=None,
            columns_json=json.dumps(columns, ensure_ascii=False),
            data_json=json.dumps(data, ensure_ascii=False),
            created_by=current_user.id
        )
        db.session.add(record)
        db.session.commit()
        print("✅ تم حفظ البيانات في قاعدة البيانات")

        return jsonify({'success': True, 'message': 'تم رفع الملف وحفظه بنجاح.', 'columns': columns, 'data': data})
    except Exception as e:
        print(f"❌ خطأ في معالجة الملف: {str(e)}")
        db.session.rollback()
        return jsonify({'success': False, 'message': f'خطأ أثناء معالجة الملف: {str(e)}'}), 500


@duties_bp.route('/api/save', methods=['POST'])
@login_required
def save_changes():
    """حفظ تعديلات الجدول في قاعدة البيانات"""
    try:
        payload = request.get_json(force=True)
        data = payload.get('data', [])
        columns = payload.get('columns', [])

        record = DutiesExcelData.query.order_by(DutiesExcelData.updated_at.desc()).first()
        if not record:
            record = DutiesExcelData(created_by=current_user.id)
            db.session.add(record)

        record.data_json = json.dumps(data, ensure_ascii=False)
        record.columns_json = json.dumps(columns, ensure_ascii=False) if columns else record.columns_json
        record.filename = record.filename or 'duties.xlsx'
        db.session.commit()

        return jsonify({'success': True, 'message': 'تم حفظ التعديلات بنجاح.'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'فشل حفظ التعديلات: {str(e)}'}), 500


@duties_bp.route('/api/export', methods=['GET'])
@login_required
def export_excel():
    """تصدير البيانات الحالية إلى ملف Excel"""
    record = DutiesExcelData.query.order_by(DutiesExcelData.updated_at.desc()).first()
    if not record:
        return jsonify({'success': False, 'message': 'لا توجد بيانات لتصديرها.'}), 400

    try:
        rows = json.loads(record.data_json)
        columns = json.loads(record.columns_json) if record.columns_json else None
        df = pd.DataFrame(rows)
        if columns:
            # ضمان ترتيب الأعمدة كما حفظت
            df = df[[c for c in columns if c in df.columns]]

        output = BytesIO()
        engine = 'xlsxwriter' if XLSXWRITER_AVAILABLE else ('openpyxl' if OPENPYXL_AVAILABLE else None)
        if engine is None or not EXCEL_AVAILABLE:
            # كبديل: تصدير CSV
            csv_bytes = df.to_csv(index=False).encode('utf-8-sig')
            output.write(csv_bytes)
            output.seek(0)
            return send_file(output, as_attachment=True, download_name='duties.csv', mimetype='text/csv; charset=utf-8')

        with pd.ExcelWriter(output, engine=engine) as writer:
            df.to_excel(writer, index=False, sheet_name='Duties')
        output.seek(0)
        return send_file(output, as_attachment=True, download_name='duties.xlsx', mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    except Exception as e:
        return jsonify({'success': False, 'message': f'فشل التصدير: {str(e)}'}), 500


@duties_bp.route('/api/delete', methods=['DELETE', 'POST'])
@login_required
def delete_current():
    """حذف الملف/البيانات الحالية"""
    try:
        DutiesExcelData.query.delete()
        db.session.commit()
        return jsonify({'success': True, 'message': 'تم حذف البيانات الحالية بنجاح.'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'فشل الحذف: {str(e)}'}), 500

