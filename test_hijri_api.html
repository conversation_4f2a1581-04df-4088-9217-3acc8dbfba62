<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار API التاريخ الهجري</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            direction: rtl;
            text-align: right;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .date-info {
            background: #e8f4fd;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-right: 4px solid #007bff;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-right: 4px solid #dc3545;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .field {
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 3px;
        }
        .label {
            font-weight: bold;
            color: #495057;
        }
        .value {
            color: #007bff;
            font-size: 1.1em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار API التاريخ الهجري</h1>
        
        <button onclick="testAPI()">اختبار API</button>
        <button onclick="clearResults()">مسح النتائج</button>
        
        <div id="results"></div>
    </div>

    <script>
        async function testAPI() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<p>جاري الاختبار...</p>';
            
            try {
                const response = await fetch('/reports/api/hijri-date');
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                
                if (data.success) {
                    resultsDiv.innerHTML = `
                        <div class="date-info">
                            <h3>✅ نجح الاختبار - البيانات المستلمة من API:</h3>
                            
                            <div class="field">
                                <span class="label">اليوم:</span>
                                <span class="value">${data.day_name}</span>
                            </div>
                            
                            <div class="field">
                                <span class="label">التاريخ الميلادي:</span>
                                <span class="value">${data.gregorian_formatted}</span>
                            </div>
                            
                            <div class="field">
                                <span class="label">التاريخ الهجري:</span>
                                <span class="value">${data.hijri_formatted}</span>
                            </div>
                            
                            <div class="field">
                                <span class="label">الوقت (24 ساعة):</span>
                                <span class="value">${data.time_24h}</span>
                            </div>
                            
                            <div class="field">
                                <span class="label">الوقت (12 ساعة):</span>
                                <span class="value">${data.time_12h}</span>
                            </div>
                            
                            <div class="field">
                                <span class="label">التفاصيل الهجرية:</span>
                                <span class="value">
                                    اليوم: ${data.hijri_day} | 
                                    الشهر: ${data.hijri_month_name} (${data.hijri_month}) | 
                                    السنة: ${data.hijri_year}
                                </span>
                            </div>
                            
                            <div class="field">
                                <span class="label">الطابع الزمني:</span>
                                <span class="value">${data.timestamp}</span>
                            </div>
                        </div>
                    `;
                } else {
                    resultsDiv.innerHTML = `
                        <div class="error">
                            <h3>❌ فشل الاختبار</h3>
                            <p>API أرجع success: false</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                }
                
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="error">
                        <h3>❌ خطأ في الاتصال بـ API</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        // اختبار تلقائي عند تحميل الصفحة
        window.addEventListener('load', testAPI);
    </script>
</body>
</html>
