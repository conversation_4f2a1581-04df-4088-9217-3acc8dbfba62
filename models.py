from datetime import datetime
from enum import Enum
from flask_login import UserMixin
from sqlalchemy import VARCHAR
from werkzeug.security import generate_password_hash, check_password_hash
from db import db
from datetime_utils import get_saudi_now

# Enums for various status fields
class WeaponStatusEnum(Enum):
    ACTIVE = 'نشط'
    LEAVE = 'إجازة'
    MISSION = 'مهمة'
    MAINTENANCE = 'صيانة'
    CYCLE = 'دورة'
    VACANT = 'شاغر'
    RECIPIENT = 'مستلم'
    SHOOTING = 'رماية'
    OTHER = 'أخرى'

class PersonnelStatusEnum(Enum):
    ACTIVE = 'نشط'
    LEAVE = 'إجازة'
    MISSION = 'مهمة'
    CYCLE = 'دورة'
    RECIPIENT = 'مستلم'
    SHOOTING = 'رماية'

class DeviceStatusEnum(Enum):
    OPERATIONAL = 'سليم'
    MINOR_DAMAGE = 'عطل بسيط'
    MAJOR_DAMAGE = 'عطل جسيم'
    MAINTENANCE = 'تحت الصيانة'
    OUT_OF_SERVICE = 'خارج الخدمة'
    MISSING = 'مفقود'

class UserRoleEnum(Enum):
    ADMIN = 'مدير نظام'
    WAREHOUSE_MANAGER = 'مدير المستودعات'
    INVENTORY_MANAGER = 'مسؤول مخزون'
    MONITOR = 'مراقب'
    COMPANY_DUTY = 'مناوب السرية'

class InventoryTypeEnum(Enum):
    CLOTHING = 'ملبوسات عسكرية'
    EQUIPMENT = 'معدات عسكرية'
    AMMUNITION = 'ذخيرة'

class InventoryStatusEnum(Enum):
    AVAILABLE = 'متوفر'
    ISSUED = 'مُصدر'
    MAINTENANCE = 'صيانة'
    DAMAGED = 'تالف'
    EXPIRED = 'منتهي الصلاحية'
    RESERVED = 'محجوز'

# Association table for user-warehouse relationship
user_warehouse = db.Table('user_warehouse',
    db.Column('user_id', db.Integer, db.ForeignKey('users.id'), primary_key=True),
    db.Column('warehouse_id', db.Integer, db.ForeignKey('warehouses.id'), primary_key=True)
)

# Association table for personnel-weapon relationship
personnel_weapon = db.Table('personnel_weapon',
    db.Column('personnel_id', db.Integer, db.ForeignKey('personnel.id'), primary_key=True),
    db.Column('weapon_id', db.Integer, db.ForeignKey('weapons.id'), primary_key=True),
    db.Column('is_primary', db.Boolean, default=False)
)

class User(UserMixin, db.Model):
    __tablename__ = 'users'
    __table_args__ = {'extend_existing': True}

    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    password_hash = db.Column(db.String(256))
    full_name = db.Column(db.String(100), nullable=True)
    # جعل حقل البريد الإلكتروني قابل للتغيير (nullable) للتوافق مع قواعد البيانات المختلفة
    email = db.Column(db.String(120), unique=True, nullable=True)
    is_admin = db.Column(db.Boolean, default=False)  # للتوافق مع الكود القديم
    # عمود لتخزين دور المستخدم بشكل دائم في قاعدة البيانات
    user_role = db.Column(db.String(50), nullable=True)
    role_en = db.Column(db.String(50), nullable=True)  # الدور باللغة الإنجليزية
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=get_saudi_now)
    last_login = db.Column(db.DateTime)
    updated_at = db.Column(db.DateTime, default=get_saudi_now, onupdate=get_saudi_now)

    # Relationships
    warehouses = db.relationship('Warehouse',
                               secondary=user_warehouse,
                               backref=db.backref('users', lazy='dynamic'),
                               lazy='joined')  # Eager loading

    def set_password(self, password_value):
        self.password_hash = generate_password_hash(password_value)

    def check_password(self, password_value):
        return check_password_hash(self.password_hash, password_value)

    @property
    def role(self):
        # إذا كان هناك دور إنجليزي محفوظ، استخدمه
        if hasattr(self, 'role_en') and self.role_en:
            return self.role_en

        # إذا كان هناك دور عربي محفوظ، حوله إلى إنجليزي
        if self.user_role:
            arabic_to_english = {
                'مدير نظام': 'admin',
                'مدير المستودعات': 'warehouse_manager',
                'مسؤول مخزون': 'inventory_manager',
                'مراقب': 'monitor',
                'مناوب السرية': 'company_duty'
            }
            return arabic_to_english.get(self.user_role, 'monitor')

        # وإلا استخدم is_admin لتحديد الدور
        return 'admin' if self.is_admin else 'monitor'

    @role.setter
    def role(self, value):
        # تحديث الدور في قاعدة البيانات مع النص العربي المناسب
        role_mapping = {
            'admin': 'مدير نظام',
            'warehouse_manager': 'مدير المستودعات',
            'inventory_manager': 'مسؤول مخزون',
            'monitor': 'مراقب',
            'company_duty': 'مناوب السرية'
        }

        # حفظ القيمة الإنجليزية في حقل منفصل
        self.role_en = value

        # تحديث النص العربي
        self.user_role = role_mapping.get(value, value)

        # تحديث is_admin للتوافق مع الكود القديم
        if value == 'admin':
            self.is_admin = True
        elif value in ['warehouse_manager', 'inventory_manager', 'monitor', 'company_duty']:
            self.is_admin = False

    # للتوافق مع الكود القديم
    def save_role_to_session(self, session):
        """ حفظ الدور في جلسة المستخدم """
        session[f'user_role_{self.id}'] = self.user_role

    def load_role_from_session(self, session):
        """ تحميل الدور من جلسة المستخدم """
        if f'user_role_{self.id}' in session:
            # تحديث الدور في قاعدة البيانات من الجلسة
            self.user_role = session[f'user_role_{self.id}']
            return True
        return False

    @property
    def is_warehouse_manager(self):
        return self.role == 'warehouse_manager'

    @property
    def is_inventory_manager(self):
        return self.role == 'inventory_manager'

    @property
    def is_monitor(self):
        return self.role == 'monitor'

    @property
    def is_company_duty(self):
        return self.role == 'company_duty' or self.user_role == 'مناوب السرية'

    # تحديث خاصية is_admin للتوافق مع الكود الجديد
    @property
    def is_admin_role(self):
        return self.role == 'admin' or self.is_admin or self.user_role == 'مدير نظام' or self.role == 'system_administrator'

    # وظائف التحقق من الصلاحيات
    def can_view(self, warehouse=None):
        """التحقق من صلاحية المستخدم للاطلاع على البيانات"""
        # مدير النظام ومدير المستودعات يمكنهم الاطلاع على كل شيء
        if self.is_admin_role or self.is_warehouse_manager:
            return True

        # إذا تم تحديد مستودع، تحقق من أن المستخدم لديه صلاحية على هذا المستودع
        if warehouse and warehouse not in self.warehouses:
            return False

        # جميع الأدوار يمكنها الاطلاع
        return True

    def can_add(self, warehouse=None):
        """التحقق من صلاحية المستخدم للإضافة"""
        # مدير النظام ومدير المستودعات يمكنهم الإضافة في أي مكان
        if self.is_admin_role or self.is_warehouse_manager:
            return True

        # مناوب السرية يمكنه الإضافة في كشف الاستلامات وإدارة المواقع (بدون قيود مستودعات)
        if self.is_company_duty:
            return True

        # إذا تم تحديد مستودع، تحقق من أن المستخدم لديه صلاحية على هذا المستودع
        if warehouse and warehouse not in self.warehouses:
            return False

        # المراقب لا يمكنه الإضافة
        if self.is_monitor:
            return False

        # مسؤول المخزون يمكنه الإضافة
        return True

    def can_edit(self, warehouse=None):
        """التحقق من صلاحية المستخدم للتعديل"""
        # مدير النظام ومدير المستودعات يمكنهم التعديل في أي مكان
        if self.is_admin_role or self.is_warehouse_manager:
            return True

        # مناوب السرية يمكنه التعديل في كشف الاستلامات وإدارة المواقع (بدون قيود مستودعات)
        if self.is_company_duty:
            return True

        # إذا تم تحديد مستودع، تحقق من أن المستخدم لديه صلاحية على هذا المستودع
        if warehouse and warehouse not in self.warehouses:
            return False

        # المراقب لا يمكنه التعديل
        if self.is_monitor:
            return False

        # مسؤول المخزون يمكنه تعديل المخزون فقط (سيتم التحقق في المسارات)
        return self.is_inventory_manager

    def can_delete(self, warehouse=None):
        """التحقق من صلاحية المستخدم للحذف"""
        # مدير النظام ومدير المستودعات يمكنهم الحذف في أي مكان
        if self.is_admin_role or self.is_warehouse_manager:
            return True

        # مناوب السرية يمكنه الحذف في كشف الاستلامات وإدارة المواقع (بدون قيود مستودعات)
        if self.is_company_duty:
            return True

        # إذا تم تحديد مستودع، تحقق من أن المستخدم لديه صلاحية على هذا المستودع
        if warehouse and warehouse not in self.warehouses:
            return False

        # المراقب لا يمكنه الحذف
        if self.is_monitor:
            return False

        # مسؤول المخزون يمكنه الحذف في مستودعه
        return self.is_inventory_manager

    def __repr__(self):
        return f'<User {self.username}>'

class Warehouse(db.Model):
    """Warehouse model for the three storage locations: Warehouse 1, Warehouse 2, and Storage."""
    __tablename__ = 'warehouses'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    location = db.Column(db.String(200))
    capacity = db.Column(db.Integer, default=0)  # الطاقة الاستيعابية القصوى للمستودع
    created_at = db.Column(db.DateTime, default=get_saudi_now)
    updated_at = db.Column(db.DateTime, default=get_saudi_now, onupdate=get_saudi_now)

    # Relationships
    weapons = db.relationship('Weapon', backref='warehouse', lazy='dynamic')
    personnel = db.relationship('Personnel', backref='warehouse', lazy='dynamic')
    # تم تعديل العلاقة مع الأجهزة لتكون اختيارية
    devices = db.relationship('Device', backref=db.backref('warehouse', uselist=False), lazy='dynamic')
    audits = db.relationship('Audit', backref='warehouse', lazy='dynamic')
    activity_logs = db.relationship('ActivityLog', backref='warehouse', lazy='dynamic')

    def get_usage_percentage(self):
        """حساب نسبة الاستخدام للمستودع"""
        if self.capacity == 0:
            return 0
        current_weapons = self.weapons.count()
        return round((current_weapons / self.capacity) * 100, 1)

    def get_available_capacity(self):
        """حساب الطاقة المتاحة في المستودع"""
        current_weapons = self.weapons.count()
        return max(0, self.capacity - current_weapons)

    def is_at_capacity(self):
        """التحقق من امتلاء المستودع"""
        return self.weapons.count() >= self.capacity if self.capacity > 0 else False

    def __repr__(self):
        return f'<Warehouse {self.name}>'

class Weapon(db.Model):
    """Weapon model for tracking all weapons across warehouses."""
    __tablename__ = 'weapons'

    id = db.Column(db.Integer, primary_key=True)
    weapon_number = db.Column(db.String(100))
    serial_number = db.Column(db.String(100), unique=True, nullable=False)
    name = db.Column(db.String(100), nullable=False)
    type = db.Column(db.String(50), nullable=False)  # Pistol, Rifle, etc.
    status = db.Column(db.String(20), nullable=False, default='نشط')
    condition = db.Column(db.String(100))
    notes = db.Column(db.Text)
    barcode = db.Column(db.String(100), unique=True)
    qr_code = db.Column(db.String(200), unique=True)
    weapon_document = db.Column(db.String(255))  # مسار ملف سند السلاح
    created_at = db.Column(db.DateTime, default=get_saudi_now)
    updated_at = db.Column(db.DateTime, default=get_saudi_now, onupdate=get_saudi_now)

    # Foreign keys
    warehouse_id = db.Column(db.Integer, db.ForeignKey('warehouses.id'), nullable=False)

    # Relationships
    transactions = db.relationship('WeaponTransaction', backref='weapon', lazy='dynamic')
    maintenance_records = db.relationship('MaintenanceRecord', backref='weapon', lazy='dynamic')
    audit_items = db.relationship('AuditItem', backref='weapon', lazy='dynamic')

    def __repr__(self):
        return f'<Weapon {self.serial_number}>'

class Personnel(db.Model):
    """Personnel model for tracking all military personnel."""
    __tablename__ = 'personnel'

    id = db.Column(db.Integer, primary_key=True)
    personnel_id = db.Column(db.String(50), unique=True, nullable=False)
    name = db.Column(db.String(100), nullable=False)
    rank = db.Column(db.String(50))
    status = db.Column(db.String(20), nullable=False, default='نشط')
    phone = db.Column(db.String(20))
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=get_saudi_now)
    updated_at = db.Column(db.DateTime, default=get_saudi_now, onupdate=get_saudi_now)

    # Foreign keys
    warehouse_id = db.Column(db.Integer, db.ForeignKey('warehouses.id'), nullable=False)

    # Many-to-many relationship with weapons
    weapons = db.relationship('Weapon', secondary=personnel_weapon, backref=db.backref('personnel', lazy='dynamic'))

    # Relationships
    transactions = db.relationship('WeaponTransaction', backref='personnel', lazy='dynamic')

    def __repr__(self):
        return f'<Personnel {self.personnel_id}>'

class WeaponTransaction(db.Model):
    """Model for tracking weapon transactions (checkout, return, transfer)."""
    __tablename__ = 'weapon_transactions'

    id = db.Column(db.Integer, primary_key=True)
    transaction_type = db.Column(db.String(20), nullable=False)  # checkout, return, transfer
    timestamp = db.Column(db.DateTime, default=get_saudi_now)
    notes = db.Column(db.Text)

    # Foreign keys
    weapon_id = db.Column(db.Integer, db.ForeignKey('weapons.id'), nullable=False)
    personnel_id = db.Column(db.Integer, db.ForeignKey('personnel.id'), nullable=True)
    source_warehouse_id = db.Column(db.Integer, db.ForeignKey('warehouses.id'), nullable=False)
    target_warehouse_id = db.Column(db.Integer, db.ForeignKey('warehouses.id'))
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)

    # Relationships
    source_warehouse = db.relationship('Warehouse', foreign_keys=[source_warehouse_id])
    target_warehouse = db.relationship('Warehouse', foreign_keys=[target_warehouse_id])
    user = db.relationship('User')

    def __repr__(self):
        return f'<WeaponTransaction {self.transaction_type} {self.timestamp}>'

class MaintenanceRecord(db.Model):
    """Model for tracking weapon maintenance history."""
    __tablename__ = 'maintenance_records'
    __table_args__ = {'extend_existing': True}

    id = db.Column(db.Integer, primary_key=True)
    maintenance_type = db.Column(db.String(50), nullable=False)
    description = db.Column(db.Text, nullable=False)
    start_date = db.Column(db.DateTime, nullable=False)
    end_date = db.Column(db.DateTime)
    status = db.Column(VARCHAR(50), nullable=False)  # Match DB's VARCHAR(50)
    cost = db.Column(VARCHAR(50))  # Match DB's VARCHAR(50)
    notes = db.Column(db.Text)
    user_id = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=get_saudi_now)
    updated_at = db.Column(db.DateTime, default=get_saudi_now, onupdate=get_saudi_now)

    # Foreign keys
    weapon_id = db.Column(db.Integer, db.ForeignKey('weapons.id'), nullable=False)

    # Note: user_id is not in the existing database schema
    # The following commented code is for reference only
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    user = db.relationship('User')

    def __repr__(self):
        return f'<MaintenanceRecord {self.maintenance_type} {self.start_date}>'

class Device(db.Model):
    """Model for tracking administrative devices like computers, printers, scanners."""
    __tablename__ = 'devices'
    __table_args__ = {'extend_existing': True}

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    type = db.Column(db.String(50), nullable=False)  # computer, laptop, printer, scanner, etc.
    model = db.Column(db.String(100))
    serial_number = db.Column(db.String(100), unique=True)
    status = db.Column(db.String(20), nullable=False, default='سليم')
    manufacturer = db.Column(db.String(100), nullable=True)
    location = db.Column(db.String(200), nullable=True)
    # ip_address = db.Column(db.String(20), default='غير معرف')
    # mac_address = db.Column(db.String(40), default='غير معرف')
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=get_saudi_now)
    updated_at = db.Column(db.DateTime, default=get_saudi_now, onupdate=get_saudi_now)

    # Foreign keys
    warehouse_id = db.Column(db.Integer, db.ForeignKey('warehouses.id'), nullable=True)

    # Relationships
    maintenance_records = db.relationship('DeviceMaintenanceRecord', backref='device', lazy='dynamic')



    def __repr__(self):
        return f'<Device {self.name}>'

class DeviceMaintenanceRecord(db.Model):
    """Model for tracking device maintenance history."""
    __tablename__ = 'device_maintenance_records'
    __table_args__ = {'extend_existing': True}

    id = db.Column(db.Integer, primary_key=True)
    maintenance_type = db.Column(db.String(50), nullable=False)
    description = db.Column(db.Text, nullable=False)
    start_date = db.Column(db.DateTime, nullable=False)
    end_date = db.Column(db.DateTime)
    status = db.Column(db.String(20), nullable=False)  # ongoing, completed, cancelled
    cost = db.Column(db.String)
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=get_saudi_now)
    updated_at = db.Column(db.DateTime, default=get_saudi_now, onupdate=get_saudi_now)

    # Foreign keys
    device_id = db.Column(db.Integer, db.ForeignKey('devices.id'), nullable=False)

    # Note: user_id is not in the existing database schema
    # The following commented code is for reference only
    # user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    # user = db.relationship('User')

    def __repr__(self):
        return f'<DeviceMaintenanceRecord {self.maintenance_type} {self.start_date}>'

class Audit(db.Model):
    """Model for tracking inventory audits."""
    __tablename__ = 'audits'
    __table_args__ = {'extend_existing': True}

    id = db.Column(db.Integer, primary_key=True)
    audit_date = db.Column(db.DateTime, nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    status = db.Column(db.String(20), nullable=False)  # in-progress, completed, cancelled
    description = db.Column(db.Text)
    notes = db.Column(db.Text)
    next_audit_date = db.Column(db.DateTime, nullable=True)  # تاريخ الجرد القادم
    completed_at = db.Column(db.DateTime, nullable=True)  # تاريخ اكتمال الجرد
    created_at = db.Column(db.DateTime, default=get_saudi_now)
    updated_at = db.Column(db.DateTime, default=get_saudi_now, onupdate=get_saudi_now)

    # Foreign keys
    warehouse_id = db.Column(db.Integer, db.ForeignKey('warehouses.id'), nullable=False)

    # Relationships
    audit_items = db.relationship('AuditItem', backref='audit', lazy='dynamic')
    user = db.relationship('User', backref='audits')

    def __repr__(self):
        return f'<Audit {self.audit_date}>'

class AuditItem(db.Model):
    """Model for tracking individual items in an audit."""
    __tablename__ = 'audit_items'

    id = db.Column(db.Integer, primary_key=True)
    status = db.Column(db.String(20), nullable=False)  # found, missing, damaged
    condition = db.Column(db.String(100))
    notes = db.Column(db.Text)
    maintenance_reason = db.Column(db.Text)  # سبب الصيانة إذا كانت الحالة damaged
    technical_notes = db.Column(db.Text)  # ملاحظات فنية للصيانة
    maintenance_record_id = db.Column(db.Integer, db.ForeignKey('maintenance_records.id'), nullable=True)  # ربط بسجل الصيانة
    created_at = db.Column(db.DateTime, default=get_saudi_now)
    updated_at = db.Column(db.DateTime, default=get_saudi_now, onupdate=get_saudi_now)

    # Foreign keys
    audit_id = db.Column(db.Integer, db.ForeignKey('audits.id'), nullable=False)
    weapon_id = db.Column(db.Integer, db.ForeignKey('weapons.id'), nullable=False)

    # Relationships
    maintenance_record = db.relationship('MaintenanceRecord', foreign_keys=[maintenance_record_id], backref=db.backref('audit_item', uselist=False))

    def __repr__(self):
        return f'<AuditItem {self.status}>'

class ActivityLog(db.Model):
    __tablename__ = 'activity_logs'
    __table_args__ = {'extend_existing': True}
    """Model for tracking all system activity across warehouses."""

    id = db.Column(db.Integer, primary_key=True)
    action = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text, nullable=False)
    timestamp = db.Column(db.DateTime, default=get_saudi_now)
    ip_address = db.Column(db.String(50))

    # Foreign keys
    user_id = db.Column(db.Integer, db.ForeignKey('users.id', ondelete='SET NULL'), nullable=True)
    warehouse_id = db.Column(db.Integer, db.ForeignKey('warehouses.id'), nullable=True)

    # Relationships
    user = db.relationship('User', backref=db.backref('activity_logs', passive_deletes=True, cascade='all'))

    def __repr__(self):
        return f'<ActivityLog {self.action} {self.timestamp}>'

class WeeklyReport(db.Model):
    """Model for storing weekly personnel status change reports."""
    __tablename__ = 'weekly_reports'
    __table_args__ = {'extend_existing': True}

    id = db.Column(db.Integer, primary_key=True)
    week_start = db.Column(db.Date, nullable=False)  # بداية الأسبوع
    week_end = db.Column(db.Date, nullable=False)    # نهاية الأسبوع
    personnel_name = db.Column(db.String(100), nullable=False)  # اسم الفرد
    personnel_id = db.Column(db.String(50), nullable=False)     # الرقم العسكري
    national_id = db.Column(db.String(20), nullable=False)      # الهوية الوطنية
    rank = db.Column(db.String(50), nullable=False)             # الرتبة
    old_status = db.Column(db.String(50), nullable=False)       # الحالة السابقة
    new_status = db.Column(db.String(50), nullable=False)       # الحالة الجديدة
    current_status = db.Column(db.String(50), nullable=False)   # الحالة الحالية
    change_date = db.Column(db.DateTime, nullable=False)        # تاريخ التغيير
    change_time = db.Column(db.String(20), nullable=False)      # وقت التغيير
    changed_by = db.Column(db.String(100), nullable=False)      # تم التغيير بواسطة
    change_method = db.Column(db.String(100), nullable=False)   # طريقة التغيير
    weapons = db.Column(db.Text)                                # الأسلحة
    warehouse_name = db.Column(db.String(100), nullable=False)  # اسم المستودع
    created_at = db.Column(db.DateTime, default=get_saudi_now)  # تاريخ الإنشاء

    # Foreign keys
    warehouse_id = db.Column(db.Integer, db.ForeignKey('warehouses.id'), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)

    # Relationships
    warehouse = db.relationship('Warehouse', backref='weekly_reports')
    user = db.relationship('User', backref='weekly_reports')

    def __repr__(self):
        return f'<WeeklyReport {self.personnel_name} {self.week_start}>'

class BackupRecord(db.Model):
    """Model for tracking backup operations."""
    __tablename__ = 'backup_records'
    __table_args__ = {'extend_existing': True}

    id = db.Column(db.Integer, primary_key=True)
    filename = db.Column(db.String(255), nullable=False)
    timestamp = db.Column(db.DateTime, default=get_saudi_now)  # Changed from backup_date to timestamp
    backup_type = db.Column(db.String(20), nullable=False)  # full, warehouse1, warehouse2, storage
    file_size = db.Column(db.Integer)  # Size in bytes
    notes = db.Column(db.Text)

    # Foreign keys
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    warehouse_id = db.Column(db.Integer, db.ForeignKey('warehouses.id'))

    # Relationships
    user = db.relationship('User')
    warehouse = db.relationship('Warehouse')

    def __repr__(self):
        return f'<BackupRecord {self.timestamp}>'

class BackupSchedule(db.Model):
    """Model for scheduling automatic backups."""
    __tablename__ = 'backup_schedules'
    __table_args__ = {'extend_existing': True}

    id = db.Column(db.Integer, primary_key=True)
    schedule_type = db.Column(db.String(20), nullable=False)  # daily, weekly, monthly
    day_of_week = db.Column(db.Integer, nullable=True)  # 0-6 (Monday-Sunday) for weekly backups
    day_of_month = db.Column(db.Integer, nullable=True)  # 1-31 for monthly backups
    hour = db.Column(db.Integer, nullable=False, default=0)  # 0-23
    minute = db.Column(db.Integer, nullable=False, default=0)  # 0-59
    is_active = db.Column(db.Boolean, default=True)
    backup_type = db.Column(db.String(20), nullable=False, default='full')  # full, warehouse
    last_run = db.Column(db.DateTime, nullable=True)
    next_run = db.Column(db.DateTime, nullable=True)
    created_at = db.Column(db.DateTime, default=get_saudi_now)
    updated_at = db.Column(db.DateTime, default=get_saudi_now, onupdate=get_saudi_now)

    # Foreign keys
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    warehouse_id = db.Column(db.Integer, db.ForeignKey('warehouses.id'), nullable=True)  # Null for full backup

    # Relationships
    user = db.relationship('User')
    warehouse = db.relationship('Warehouse')

    def __repr__(self):
        return f'<BackupSchedule {self.schedule_type} {self.backup_type}>'

class InventoryItem(db.Model):
    """Model for tracking military clothing, equipment, and ammunition inventory."""
    __tablename__ = 'inventory_items'

    id = db.Column(db.Integer, primary_key=True)
    item_code = db.Column(db.String(100), unique=True, nullable=False)  # كود الصنف
    name = db.Column(db.String(200), nullable=False)  # اسم الصنف
    category = db.Column(db.String(50), nullable=False)  # ملبوسات/معدات/ذخيرة
    subcategory = db.Column(db.String(100))  # فئة فرعية
    brand = db.Column(db.String(100))  # الماركة/الشركة المصنعة
    model = db.Column(db.String(100))  # الموديل
    size = db.Column(db.String(50))  # المقاس (للملبوسات)
    color = db.Column(db.String(50))  # اللون
    material = db.Column(db.String(100))  # المادة المصنوع منها

    # معلومات الكمية والمخزون
    quantity_in_stock = db.Column(db.Integer, default=0)  # الكمية المتوفرة
    quantity_issued = db.Column(db.Integer, default=0)  # الكمية المُصدرة
    minimum_stock = db.Column(db.Integer, default=0)  # الحد الأدنى للمخزون
    maximum_stock = db.Column(db.Integer, default=0)  # الحد الأقصى للمخزون

    # معلومات التكلفة والسعر
    unit_cost = db.Column(db.Numeric(10, 2))  # تكلفة الوحدة
    total_value = db.Column(db.Numeric(12, 2))  # القيمة الإجمالية

    # معلومات التواريخ
    manufacture_date = db.Column(db.DateTime)  # تاريخ التصنيع
    expiry_date = db.Column(db.DateTime)  # تاريخ انتهاء الصلاحية
    purchase_date = db.Column(db.DateTime)  # تاريخ الشراء

    # معلومات الحالة والموقع
    status = db.Column(db.String(50), default='متوفر')  # الحالة
    location = db.Column(db.String(200))  # الموقع في المستودع
    shelf_number = db.Column(db.String(50))  # رقم الرف

    # معلومات إضافية
    supplier = db.Column(db.String(200))  # المورد
    batch_number = db.Column(db.String(100))  # رقم الدفعة
    serial_numbers = db.Column(db.Text)  # الأرقام التسلسلية (للمعدات)
    specifications = db.Column(db.Text)  # المواصفات التقنية
    notes = db.Column(db.Text)  # ملاحظات

    # معلومات النظام
    created_at = db.Column(db.DateTime, default=get_saudi_now)
    updated_at = db.Column(db.DateTime, default=get_saudi_now, onupdate=get_saudi_now)

    # Foreign keys
    warehouse_id = db.Column(db.Integer, db.ForeignKey('warehouses.id'), nullable=False)

    # Relationships
    transactions = db.relationship('InventoryTransaction', backref='item', lazy='dynamic')

    def __repr__(self):
        return f'<InventoryItem {self.item_code}: {self.name}>'

    @property
    def total_quantity(self):
        """إجمالي الكمية (متوفرة + مُصدرة)"""
        return (self.quantity_in_stock or 0) + (self.quantity_issued or 0)

    @property
    def is_low_stock(self):
        """تحقق من انخفاض المخزون"""
        return self.quantity_in_stock <= self.minimum_stock

    @property
    def is_expired(self):
        """تحقق من انتهاء الصلاحية"""
        if not self.expiry_date:
            return False
        return self.expiry_date < get_saudi_now()

    @property
    def days_to_expiry(self):
        """عدد الأيام المتبقية لانتهاء الصلاحية"""
        if not self.expiry_date:
            return None
        delta = self.expiry_date - get_saudi_now()
        return delta.days if delta.days > 0 else 0

class InventoryTransaction(db.Model):
    """Model for tracking inventory transactions (issue, return, transfer, adjustment)."""
    __tablename__ = 'inventory_transactions'

    id = db.Column(db.Integer, primary_key=True)
    transaction_type = db.Column(db.String(50), nullable=False)  # issue, return, transfer, adjustment, receive
    quantity = db.Column(db.Integer, nullable=False)  # الكمية
    unit_cost = db.Column(db.Numeric(10, 2))  # تكلفة الوحدة وقت المعاملة
    total_cost = db.Column(db.Numeric(12, 2))  # التكلفة الإجمالية

    # معلومات المعاملة
    reference_number = db.Column(db.String(100))  # رقم المرجع
    recipient_name = db.Column(db.String(200))  # اسم المستلم
    recipient_id = db.Column(db.String(100))  # رقم هوية المستلم
    recipient_rank = db.Column(db.String(50))  # رتبة المستلم
    recipient_unit = db.Column(db.String(200))  # وحدة المستلم

    # معلومات التحويل (في حالة النقل)
    from_warehouse_id = db.Column(db.Integer, db.ForeignKey('warehouses.id'))
    to_warehouse_id = db.Column(db.Integer, db.ForeignKey('warehouses.id'))

    # معلومات إضافية
    reason = db.Column(db.String(200))  # سبب المعاملة
    notes = db.Column(db.Text)  # ملاحظات
    approved_by = db.Column(db.String(200))  # معتمد من

    # معلومات النظام
    transaction_date = db.Column(db.DateTime, default=get_saudi_now)
    created_at = db.Column(db.DateTime, default=get_saudi_now)
    updated_at = db.Column(db.DateTime, default=get_saudi_now, onupdate=get_saudi_now)

    # Foreign keys
    item_id = db.Column(db.Integer, db.ForeignKey('inventory_items.id'), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)

    # Relationships
    user = db.relationship('User')
    from_warehouse = db.relationship('Warehouse', foreign_keys=[from_warehouse_id])
    to_warehouse = db.relationship('Warehouse', foreign_keys=[to_warehouse_id])

    def __repr__(self):
        return f'<InventoryTransaction {self.transaction_type} {self.quantity}>'


class Location(db.Model):
    """نموذج المواقع"""
    __tablename__ = 'locations'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    serial_number = db.Column(db.String(50), unique=True, nullable=False)
    type = db.Column(db.String(50), nullable=False, default='أمني')  # أمني، إداري، خدمي، سكني
    status = db.Column(db.String(50), nullable=False, default='نشط')  # نشط، غير نشط، مسحوب، تحت الصيانة
    coordinates = db.Column(db.Text)  # إحداثيات الموقع
    description = db.Column(db.Text)  # وصف الموقع
    instructions_file = db.Column(db.String(255))  # ملف التعليمات
    created_at = db.Column(db.DateTime, default=get_saudi_now)
    updated_at = db.Column(db.DateTime, default=get_saudi_now, onupdate=get_saudi_now)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))

    # العلاقات
    creator = db.relationship('User', backref='created_locations')
    equipment = db.relationship('LocationEquipment', backref='location', lazy='dynamic', cascade='all, delete-orphan')
    personnel_assignments = db.relationship('LocationPersonnel', backref='location', lazy='dynamic', cascade='all, delete-orphan')

    @property
    def personnel(self):
        """الحصول على الأفراد النشطين المعينين لهذا الموقع"""
        active_assignments = self.personnel_assignments.filter_by(is_active=True).all()
        return [assignment.personnel for assignment in active_assignments if assignment.personnel]

    def __repr__(self):
        return f'<Location {self.name}>'

    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'serial_number': self.serial_number,
            'type': self.type,
            'status': self.status,
            'coordinates': self.coordinates,
            'description': self.description,
            'instructions_file': self.instructions_file,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'equipment_count': self.equipment.count(),
            'personnel_count': self.personnel_assignments.count()
        }


class LocationEquipment(db.Model):
    """نموذج عهد المواقع"""
    __tablename__ = 'location_equipment'

    id = db.Column(db.Integer, primary_key=True)
    location_id = db.Column(db.Integer, db.ForeignKey('locations.id'), nullable=False)
    equipment_name = db.Column(db.String(200), nullable=False)
    equipment_type = db.Column(db.String(100))  # نوع العهدة
    serial_number = db.Column(db.String(100))
    quantity = db.Column(db.Integer, default=1)
    condition_status = db.Column(db.String(50), default='جيد')  # جيد، تالف، تحت الصيانة
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=get_saudi_now)
    updated_at = db.Column(db.DateTime, default=get_saudi_now, onupdate=get_saudi_now)

    def __repr__(self):
        return f'<LocationEquipment {self.equipment_name}>'


class LocationPersonnel(db.Model):
    """نموذج تعيين الأفراد للمواقع"""
    __tablename__ = 'location_personnel'

    id = db.Column(db.Integer, primary_key=True)
    location_id = db.Column(db.Integer, db.ForeignKey('locations.id'), nullable=False)
    personnel_id = db.Column(db.Integer, db.ForeignKey('personnel.id'), nullable=False)
    assignment_date = db.Column(db.DateTime, default=get_saudi_now)
    end_date = db.Column(db.DateTime)  # تاريخ انتهاء التعيين
    is_active = db.Column(db.Boolean, default=True)
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=get_saudi_now)
    updated_at = db.Column(db.DateTime, default=get_saudi_now, onupdate=get_saudi_now)

    # العلاقات
    personnel = db.relationship('Personnel', backref='location_assignments')

    def __repr__(self):
        return f'<LocationPersonnel {self.personnel.name if self.personnel else "Unknown"} at {self.location.name if self.location else "Unknown"}>'




# نماذج البيانات المؤقتة المنقولة من SQLite إلى PostgreSQL

class ReceiptData(db.Model):
    """نموذج بيانات الكشوفات"""
    __tablename__ = 'receipt_data'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, default=1)
    receipt_data = db.Column(db.Text, nullable=False)  # JSON data
    created_at = db.Column(db.DateTime, default=get_saudi_now)
    updated_at = db.Column(db.DateTime, default=get_saudi_now, onupdate=get_saudi_now)

    # العلاقات
    user = db.relationship('User', backref='receipt_data_records')

    def __repr__(self):
        return f'<ReceiptData {self.id}>'


class ReceiptLocations(db.Model):
    """نموذج مواقع الكشوفات"""
    __tablename__ = 'receipt_locations'

    id = db.Column(db.Integer, primary_key=True)
    row_index = db.Column(db.Integer, nullable=False)
    location_id = db.Column(db.String(50), nullable=False)
    timestamp = db.Column(db.String(50), nullable=False)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), default=1)
    created_at = db.Column(db.DateTime, default=get_saudi_now)

    # العلاقات
    creator = db.relationship('User', backref='receipt_locations_created')

    def __repr__(self):
        return f'<ReceiptLocation {self.location_id} at row {self.row_index}>'


class PatrolData(db.Model):
    """نموذج بيانات الدوريات"""
    __tablename__ = 'patrol_data'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, default=1)
    patrol_data = db.Column(db.Text, nullable=False)  # JSON data
    created_at = db.Column(db.DateTime, default=get_saudi_now)
    updated_at = db.Column(db.DateTime, default=get_saudi_now, onupdate=get_saudi_now)

    # العلاقات
    user = db.relationship('User', backref='patrol_data_records')

    def __repr__(self):
        return f'<PatrolData {self.id}>'


class PatrolLocations(db.Model):
    """نموذج مواقع الدوريات"""
    __tablename__ = 'patrol_locations'

    id = db.Column(db.Integer, primary_key=True)
    row_index = db.Column(db.Integer, nullable=False)
    location_id = db.Column(db.String(50), nullable=False)
    timestamp = db.Column(db.String(50), nullable=False)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), default=1)
    created_at = db.Column(db.DateTime, default=get_saudi_now)

    # العلاقات
    creator = db.relationship('User', backref='patrol_locations_created')

    def __repr__(self):
        return f'<PatrolLocation {self.location_id} at row {self.row_index}>'


class ShiftsData(db.Model):
    """نموذج بيانات المناوبات"""
    __tablename__ = 'shifts_data'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, default=1)
    shifts_data = db.Column(db.Text, nullable=False)  # JSON data
    created_at = db.Column(db.DateTime, default=get_saudi_now)
    updated_at = db.Column(db.DateTime, default=get_saudi_now, onupdate=get_saudi_now)

    # العلاقات
    user = db.relationship('User', backref='shifts_data_records')

    def __repr__(self):
        return f'<ShiftsData {self.id}>'


class ShiftsLocations(db.Model):
    """نموذج مواقع المناوبات"""
    __tablename__ = 'shifts_locations'

    id = db.Column(db.Integer, primary_key=True)
    row_index = db.Column(db.Integer, nullable=False)
    location_id = db.Column(db.String(50), nullable=False)
    timestamp = db.Column(db.String(50), nullable=False)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), default=1)
    created_at = db.Column(db.DateTime, default=get_saudi_now)

    # العلاقات
    creator = db.relationship('User', backref='shifts_locations_created')

    def __repr__(self):
        return f'<ShiftsLocation {self.location_id} at row {self.row_index}>'



# ===== نماذج صفحة كشف الاستلامات (منفصلة عن صفحة كشف الواجبات) =====

class ReceiptsPatrolData(db.Model):
    """نموذج بيانات جدول الدوريات في صفحة كشف الاستلامات"""
    __tablename__ = 'receipts_patrol_data'

    id = db.Column(db.Integer, primary_key=True)
    data_json = db.Column(db.Text, nullable=False)  # البيانات كـ JSON
    timestamp = db.Column(db.String(50))
    created_by = db.Column(db.Integer, default=0)  # 0 = مشتركة لجميع المستخدمين
    created_at = db.Column(db.DateTime, default=get_saudi_now)
    updated_at = db.Column(db.DateTime, default=get_saudi_now, onupdate=get_saudi_now)

    def __repr__(self):
        return f'<ReceiptsPatrolData {self.id}>'

class ReceiptsPatrolLocations(db.Model):
    """نموذج مواقع جدول الدوريات في صفحة كشف الاستلامات"""
    __tablename__ = 'receipts_patrol_locations'

    id = db.Column(db.Integer, primary_key=True)
    row_index = db.Column(db.Integer, nullable=False)
    location_id = db.Column(db.String(50), nullable=False)
    timestamp = db.Column(db.String(50))
    created_by = db.Column(db.Integer, default=0)  # 0 = مشتركة لجميع المستخدمين
    created_at = db.Column(db.DateTime, default=get_saudi_now)

    def __repr__(self):
        return f'<ReceiptsPatrolLocations row:{self.row_index} location:{self.location_id}>'

class ReceiptsShiftsData(db.Model):
    """نموذج بيانات جدول المناوبين في صفحة كشف الاستلامات"""
    __tablename__ = 'receipts_shifts_data'

    id = db.Column(db.Integer, primary_key=True)
    data_json = db.Column(db.Text, nullable=False)  # البيانات كـ JSON
    timestamp = db.Column(db.String(50))
    created_by = db.Column(db.Integer, default=0)  # 0 = مشتركة لجميع المستخدمين
    created_at = db.Column(db.DateTime, default=get_saudi_now)
    updated_at = db.Column(db.DateTime, default=get_saudi_now, onupdate=get_saudi_now)

    def __repr__(self):
        return f'<ReceiptsShiftsData {self.id}>'

class ReceiptsShiftsLocations(db.Model):
    """نموذج مواقع جدول المناوبين في صفحة كشف الاستلامات"""
    __tablename__ = 'receipts_shifts_locations'

    id = db.Column(db.Integer, primary_key=True)
    row_index = db.Column(db.Integer, nullable=False)
    location_id = db.Column(db.String(50), nullable=False)
    timestamp = db.Column(db.String(50))
    created_by = db.Column(db.Integer, default=0)  # 0 = مشتركة لجميع المستخدمين
    created_at = db.Column(db.DateTime, default=get_saudi_now)

    def __repr__(self):
        return f'<ReceiptsShiftsLocations row:{self.row_index} location:{self.location_id}>'

# End of models


# ===== نماذج صفحة كشف الواجبات (إدارة ملفات إكسل عامة) =====
class DutiesExcelData(db.Model):
    """تخزين بيانات ملف الإكسل (كشف الواجبات) بصيغة JSON مع معلومات الملف"""
    __tablename__ = 'duties_excel_data'

    id = db.Column(db.Integer, primary_key=True)
    filename = db.Column(db.String(255), nullable=True)
    sheet_name = db.Column(db.String(255), nullable=True)
    columns_json = db.Column(db.Text, nullable=True)  # قائمة الأعمدة (اختياري)
    data_json = db.Column(db.Text, nullable=False)    # الصفوف كـ JSON
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    created_at = db.Column(db.DateTime, default=get_saudi_now)
    updated_at = db.Column(db.DateTime, default=get_saudi_now, onupdate=get_saudi_now)

    def __repr__(self):
        return f'<DutiesExcelData {self.id} file:{self.filename}>'
