#!/usr/bin/env python
# -*- coding: utf-8 -*-

import requests
import os

# إنشاء جلسة للحفاظ على الكوكيز
session = requests.Session()

# محاولة الوصول للصفحة الرئيسية أولاً
try:
    response = session.get('http://127.0.0.1:5000/duties/')
    print(f'🌐 الوصول للصفحة: {response.status_code}')
    if response.status_code == 302:
        print('🔐 يتطلب تسجيل دخول')
        print(f'📍 إعادة توجيه إلى: {response.headers.get("Location", "غير محدد")}')
    elif response.status_code == 200:
        print('✅ تم الوصول للصفحة بنجاح')
except Exception as e:
    print(f'❌ خطأ في الوصول للصفحة: {e}')

# اختبار رفع ملف Excel
url = 'http://127.0.0.1:5000/duties/api/upload'
file_path = 'duties_test.xlsx'

if os.path.exists(file_path):
    with open(file_path, 'rb') as f:
        files = {'file': f}
        try:
            response = session.post(url, files=files)
            print(f'📤 رفع الملف: {response.status_code}')
            print(f'📋 محتوى الاستجابة: {response.text[:500]}')
            if response.headers.get('content-type', '').startswith('application/json'):
                print(f'📋 JSON: {response.json()}')
        except Exception as e:
            print(f'❌ خطأ في الرفع: {e}')
else:
    print(f'❌ الملف غير موجود: {file_path}')

# اختبار جلب البيانات
try:
    response = session.get('http://127.0.0.1:5000/duties/api/data')
    print(f'📥 جلب البيانات: {response.status_code}')
    print(f'📋 محتوى الاستجابة: {response.text[:500]}')
    if response.headers.get('content-type', '').startswith('application/json'):
        data = response.json()
        print(f'📊 عدد الصفوف: {len(data.get("data", []))}')
        print(f'📋 الأعمدة: {data.get("columns", [])}')
except Exception as e:
    print(f'❌ خطأ في جلب البيانات: {e}')
