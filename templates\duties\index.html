{% extends "base.html" %}

{% block title %}كشف الواجبات{% endblock %}

{% block styles %}
<link rel="stylesheet" href="https://unpkg.com/tabulator-tables@5.6.2/dist/css/tabulator.min.css">
<style>
  .duties-header { display:flex; align-items:center; justify-content:space-between; gap:10px; }
  .duties-actions .btn { margin-inline-start: 6px; }
  .tabulator { background: var(--bg-primary); color: var(--text-primary); }
  .tabulator .tabulator-header { background: var(--bg-secondary); color: var(--text-primary); border-color: var(--border-color); }
  .tabulator .tabulator-tableholder .tabulator-table { background: var(--bg-primary); color: var(--text-primary); }
  .tabulator-row { background: var(--bg-primary); color: var(--text-primary); }
  .tabulator-cell { border-color: var(--border-color); }
  .tabulator .tabulator-footer { background: var(--bg-secondary); color: var(--text-primary); }
  .tabulator .tabulator-page, .tabulator .tabulator-page.active { background: var(--bg-tertiary); color: var(--text-primary); border-color: var(--border-color); }
  .tabulator .tabulator-col, .tabulator .tabulator-col-row-handle { border-color: var(--border-color); }
  body.light-theme .tabulator { color: var(--text-primary); }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
  <div class="duties-header mb-3">
    <div>
      <h3 style="color: var(--text-primary)"><i class="fas fa-tasks"></i> كشف الواجبات</h3>
      <small class="text-muted">إدارة ملفات إكسل: رفع، عرض، تعديل، حفظ، تصدير، حذف</small>
    </div>
    <div class="duties-actions">
      <label class="btn btn-primary mb-0">
        <i class="fas fa-upload"></i> رفع ملف
        <input type="file" id="fileInput" accept=".xlsx,.xls" hidden>
      </label>
    </div>
  </div>

  <div id="duties-table"></div>

  <div class="d-flex justify-content-end mt-3 gap-2">
    <button id="saveBtn" class="btn btn-success"><i class="fas fa-save"></i> حفظ</button>
    <button id="exportBtn" class="btn btn-info"><i class="fas fa-file-excel"></i> تصدير</button>
    <button id="deleteBtn" class="btn btn-danger"><i class="fas fa-trash"></i> حذف</button>
  </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://unpkg.com/tabulator-tables@5.6.2/dist/js/tabulator.min.js"></script>
<script>
(function(){
  const tableEl = document.getElementById('duties-table');
  let table = null;
  let currentColumns = [];

  function notifySuccess(msg){ if (window.notifications) window.notifications.success(msg, 4000); }
  function notifyError(msg){ if (window.notifications) window.notifications.error(msg, 6000); }
  function notifyInfo(msg){ if (window.notifications) window.notifications.info(msg, 3000); }

  function buildColumnsFromData(data){
    if (!data || data.length === 0) return [];
    const keys = Object.keys(data[0]);
    return keys.map(k => ({ title: k, field: k, editor: 'input', headerSort: true }));
  }

  function initTable(columns, data){
    currentColumns = columns && columns.length ? columns : (data && data.length ? Object.keys(data[0]) : []);
    const tabCols = (columns && columns.length ? columns : currentColumns).map(c => ({
      title: String(c), field: String(c), editor: 'input', headerSort: true
    }));

    if (table) { table.destroy(); table = null; }
    table = new Tabulator(tableEl, {
      data: data || [],
      layout: 'fitColumns',
      movableColumns: true,
      reactiveData: true,
      pagination: true,
      paginationSize: 15,
      paginationSizeSelector: [10, 15, 25, 50],
      columns: tabCols,
      langs:{
        "ar-ar":{
          "pagination":{
            "first":"الأولى","first_title":"الأولى","last":"الأخيرة","last_title":"الأخيرة","prev":"السابق","prev_title":"السابق","next":"التالي","next_title":"التالي" },
          "columns":{"name":"عمود"},
        }
      }
    });
    table.setLocale("ar-ar");
  }

  async function loadInitial(){
    try{
      const res = await fetch('/duties/api/data');
      const js = await res.json();
      const cols = js.columns && js.columns.length ? js.columns : null;
      initTable(cols, js.data || []);
      if (js.message) notifyInfo(js.message);
    }catch(e){ notifyError('فشل تحميل البيانات الأولية'); }
  }

  async function uploadFile(file){
    const fd = new FormData();
    fd.append('file', file);
    try{
      const res = await fetch('/duties/api/upload', { method:'POST', body: fd });
      const js = await res.json();
      if (!js.success) throw new Error(js.message || 'فشل الرفع');
      initTable(js.columns, js.data);
      notifySuccess('تم رفع الملف وحفظه بنجاح');
    }catch(e){ notifyError(e.message || 'حدث خطأ أثناء رفع الملف'); }
  }

  async function saveChanges(){
    try{
      const data = table ? table.getData() : [];
      const cols = table ? table.getColumnDefinitions().map(c=>c.field) : currentColumns;
      const res = await fetch('/duties/api/save', {
        method:'POST', headers:{'Content-Type':'application/json'},
        body: JSON.stringify({ data, columns: cols })
      });
      const js = await res.json();
      if (!js.success) throw new Error(js.message||'فشل الحفظ');
      notifySuccess('تم حفظ التعديلات بنجاح');
    }catch(e){ notifyError(e.message || 'حدث خطأ أثناء الحفظ'); }
  }

  async function exportData(){
    try{
      const res = await fetch('/duties/api/export');
      if (res.headers.get('content-type') && res.headers.get('content-type').includes('application/json')){
        const js = await res.json(); throw new Error(js.message||'لا يمكن التصدير الآن');
      }
      const blob = await res.blob();
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a'); a.href = url; a.download = 'duties.xlsx'; a.click(); URL.revokeObjectURL(url);
      notifySuccess('تم تصدير البيانات بنجاح');
    }catch(e){ notifyError(e.message || 'حدث خطأ أثناء التصدير'); }
  }

  async function deleteData(){
    if (!confirm('هل أنت متأكد من حذف البيانات الحالية؟')) return;
    try{
      const res = await fetch('/duties/api/delete', { method:'POST' });
      const js = await res.json();
      if (!js.success) throw new Error(js.message||'فشل الحذف');
      initTable([], []);
      notifySuccess('تم حذف البيانات الحالية بنجاح');
    }catch(e){ notifyError(e.message || 'حدث خطأ أثناء الحذف'); }
  }

  // Events
  document.getElementById('fileInput').addEventListener('change', (e)=>{
    const file = e.target.files[0]; if (file) uploadFile(file);
    e.target.value = '';
  });
  document.getElementById('saveBtn').addEventListener('click', saveChanges);
  document.getElementById('exportBtn').addEventListener('click', exportData);
  document.getElementById('deleteBtn').addEventListener('click', deleteData);

  loadInitial();
})();
</script>
{% endblock %}

