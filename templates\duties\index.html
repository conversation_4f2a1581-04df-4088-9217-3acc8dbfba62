{% extends "base.html" %}

{% block title %}كشف الواجبات{% endblock %}

{% block styles %}
<style>
  .duties-header { display:flex; align-items:center; justify-content:space-between; gap:10px; }
  .duties-actions .btn { margin-inline-start: 6px; }

  /* Tabulator Styles - محلي بدلاً من CDN */
  .tabulator {
    background: var(--bg-primary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    font-family: inherit;
    font-size: 14px;
    width: 100%;
    border-radius: 4px;
  }

  .tabulator .tabulator-header {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border-bottom: 1px solid var(--border-color);
  }

  .tabulator .tabulator-col {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border-right: 1px solid var(--border-color);
    padding: 8px;
    font-weight: bold;
    text-align: center;
  }

  .tabulator .tabulator-tableholder .tabulator-table {
    background: var(--bg-primary);
    color: var(--text-primary);
  }

  .tabulator-row {
    background: var(--bg-primary);
    color: var(--text-primary);
    border-bottom: 1px solid var(--border-color);
  }

  .tabulator-row:hover {
    background: var(--bg-tertiary);
  }

  .tabulator-cell {
    border-right: 1px solid var(--border-color);
    padding: 6px 8px;
    text-align: center;
  }

  .tabulator .tabulator-footer {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border-top: 1px solid var(--border-color);
    padding: 8px;
  }

  .tabulator .tabulator-page, .tabulator .tabulator-page.active {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    margin: 0 2px;
    padding: 4px 8px;
    cursor: pointer;
  }

  .tabulator .tabulator-page:hover {
    background: var(--accent-color);
    color: #333;
  }

  .tabulator .tabulator-page.active {
    background: var(--accent-color);
    color: #333;
  }

  /* تحسينات إضافية */
  .tabulator-cell input {
    background: transparent;
    border: none;
    color: var(--text-primary);
    width: 100%;
    padding: 2px;
    text-align: center;
  }

  .tabulator-cell input:focus {
    background: var(--bg-tertiary);
    outline: 1px solid var(--accent-color);
  }

  /* رسالة عدم وجود بيانات */
  .no-data-message {
    text-align: center;
    padding: 40px;
    color: var(--text-secondary);
    font-style: italic;
  }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
  <div class="duties-header mb-3">
    <div>
      <h3 style="color: var(--text-primary)"><i class="fas fa-tasks"></i> كشف الواجبات</h3>
      <small class="text-muted">إدارة ملفات إكسل: رفع، عرض، تعديل، حفظ، تصدير، حذف</small>
    </div>
    <div class="duties-actions d-flex align-items-center">
      <div class="me-2">
        <label class="btn btn-primary mb-0">
          <i class="fas fa-upload"></i> رفع ملف
          <input type="file" id="fileInput" accept=".xlsx,.xls" hidden>
        </label>
      </div>
      <div>
        <input id="searchInput" type="text" class="form-control" placeholder="بحث..." style="min-width:220px">
      </div>
    </div>
  </div>

  <div id="duties-table">
    <div class="no-data-message">
      <i class="fas fa-table"></i><br>
      لا توجد بيانات حالياً. يرجى رفع ملف Excel لبدء العمل.
    </div>
  </div>

  <div class="d-flex justify-content-end mt-3 gap-2">
    <button id="saveBtn" class="btn btn-success"><i class="fas fa-save"></i> حفظ</button>
    <button id="exportBtn" class="btn btn-info"><i class="fas fa-file-excel"></i> تصدير</button>
    <button id="deleteBtn" class="btn btn-danger"><i class="fas fa-trash"></i> حذف</button>
  </div>
</div>
{% endblock %}

{% block scripts %}
<script>
(function(){
  const tableEl = document.getElementById('duties-table');
  let currentData = [];
  let currentColumns = [];
  let filteredData = [];

  function notifySuccess(msg){ if (window.notifications) window.notifications.success(msg, 4000); }
  function notifyError(msg){ if (window.notifications) window.notifications.error(msg, 6000); }
  function notifyInfo(msg){ if (window.notifications) window.notifications.info(msg, 3000); }

  function initTable(columns, data){
    currentColumns = columns && columns.length ? columns : (data && data.length ? Object.keys(data[0]) : []);
    currentData = data || [];
    filteredData = [...currentData];
    renderTable();
  }

  function renderTable(){
    if (!currentColumns.length || !filteredData.length) {
      tableEl.innerHTML = '<div class="no-data-message"><i class="fas fa-table"></i><br>لا توجد بيانات لعرضها</div>';
      return;
    }

    let html = '<div class="table-responsive"><table class="table table-bordered table-striped">';

    // رأس الجدول
    html += '<thead class="table-dark"><tr>';
    html += '<th style="width:50px">#</th>';
    currentColumns.forEach(col => {
      html += `<th>${col}</th>`;
    });
    html += '</tr></thead>';

    // بيانات الجدول
    html += '<tbody>';
    filteredData.forEach((row, index) => {
      html += '<tr>';
      html += `<td class="text-center fw-bold">${index + 1}</td>`;
      currentColumns.forEach(col => {
        const value = row[col] || '';
        html += `<td><input type="text" class="form-control form-control-sm border-0 bg-transparent text-center"
                     value="${String(value).replace(/"/g, '&quot;')}"
                     onchange="updateCell(${index}, '${col}', this.value)"></td>`;
      });
      html += '</tr>';
    });
    html += '</tbody></table></div>';

    // معلومات إضافية
    html += `<div class="mt-2 text-muted text-center">
      <small>📊 ${filteredData.length} صف | 📋 ${currentColumns.length} عمود</small>
    </div>`;

    tableEl.innerHTML = html;
  }

  window.updateCell = function(rowIndex, column, value) {
    if (filteredData[rowIndex]) {
      filteredData[rowIndex][column] = value;
      // تحديث البيانات الأصلية أيضاً
      const originalIndex = currentData.findIndex(row => row === filteredData[rowIndex]);
      if (originalIndex !== -1) {
        currentData[originalIndex][column] = value;
      }
    }
  };

  window.getCurrentData = function() {
    return currentData;
  };

  window.getCurrentColumns = function() {
    return currentColumns;
  };

  async function loadInitial(){
    try{
      const res = await fetch('/duties/api/data');
      const js = await res.json();
      const cols = js.columns && js.columns.length ? js.columns : null;
      initTable(cols, js.data || []);
      if (js.message) notifyInfo(js.message);
    }catch(e){ notifyError('فشل تحميل البيانات الأولية'); }
  }

  async function uploadFile(file){
    const fd = new FormData();
    fd.append('file', file);
    try{
      const res = await fetch('/duties/api/upload', { method:'POST', body: fd });
      const js = await res.json();
      if (!js.success) throw new Error(js.message || 'فشل الرفع');
      initTable(js.columns, js.data);
      notifySuccess('تم رفع الملف وحفظه بنجاح');
    }catch(e){ notifyError(e.message || 'حدث خطأ أثناء رفع الملف'); }
  }

  async function saveChanges(){
    try{
      const data = getCurrentData();
      const cols = getCurrentColumns();
      const res = await fetch('/duties/api/save', {
        method:'POST', headers:{'Content-Type':'application/json'},
        body: JSON.stringify({ data, columns: cols })
      });
      const js = await res.json();
      if (!js.success) throw new Error(js.message||'فشل الحفظ');
      notifySuccess('تم حفظ التعديلات بنجاح');
    }catch(e){ notifyError(e.message || 'حدث خطأ أثناء الحفظ'); }
  }

  async function exportData(){
    try{
      const res = await fetch('/duties/api/export');
      if (res.headers.get('content-type') && res.headers.get('content-type').includes('application/json')){
        const js = await res.json(); throw new Error(js.message||'لا يمكن التصدير الآن');
      }
      const blob = await res.blob();
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a'); a.href = url; a.download = 'duties.xlsx'; a.click(); URL.revokeObjectURL(url);
      notifySuccess('تم تصدير البيانات بنجاح');
    }catch(e){ notifyError(e.message || 'حدث خطأ أثناء التصدير'); }
  }

  async function deleteData(){
    if (!confirm('هل أنت متأكد من حذف البيانات الحالية؟')) return;
    try{
      const res = await fetch('/duties/api/delete', { method:'POST' });
      const js = await res.json();
      if (!js.success) throw new Error(js.message||'فشل الحذف');
      initTable([], []);
      notifySuccess('تم حذف البيانات الحالية بنجاح');
    }catch(e){ notifyError(e.message || 'حدث خطأ أثناء الحذف'); }
  }

  // Events
  document.getElementById('fileInput').addEventListener('change', (e)=>{
    const file = e.target.files[0]; if (file) uploadFile(file);
    e.target.value = '';
  });
  document.getElementById('saveBtn').addEventListener('click', saveChanges);
  document.getElementById('exportBtn').addEventListener('click', exportData);
  document.getElementById('deleteBtn').addEventListener('click', deleteData);
  document.getElementById('searchInput').addEventListener('input', (e)=>{
    const term = e.target.value.trim().toLowerCase();
    if (!term) {
      filteredData = [...currentData];
    } else {
      filteredData = currentData.filter(row => {
        return currentColumns.some(col => {
          const value = String(row[col] || '').toLowerCase();
          return value.includes(term);
        });
      });
    }
    renderTable();
  });

  loadInitial();
})();
</script>
{% endblock %}

