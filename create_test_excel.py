#!/usr/bin/env python
# -*- coding: utf-8 -*-

import pandas as pd
import os

# إنشاء بيانات تجريبية لكشف الواجبات
data = {
    'الرقم': [1, 2, 3, 4, 5],
    'اسم الفرد': ['أحمد محمد', 'سعد علي', 'محمد سالم', 'عبدالله أحمد', 'فهد محمد'],
    'الرتبة': ['عريف', 'جندي أول', 'رقيب', 'عريف', 'جندي'],
    'الوحدة': ['الوحدة الأولى', 'الوحدة الثانية', 'الوحدة الأولى', 'الوحدة الثالثة', 'الوحدة الثانية'],
    'نوع الواجب': ['حراسة', 'دورية', 'مراقبة', 'حراسة', 'دورية'],
    'الموقع': ['البوابة الرئيسية', 'المحيط الخارجي', 'برج المراقبة', 'البوابة الجنوبية', 'الساحة الداخلية'],
    'وقت البداية': ['06:00', '14:00', '22:00', '06:00', '14:00'],
    'وقت النهاية': ['14:00', '22:00', '06:00', '14:00', '22:00'],
    'ملاحظات': ['', 'تم بنجاح', '', 'تأخير 10 دقائق', '']
}

df = pd.DataFrame(data)
filename = 'duties_test.xlsx'
df.to_excel(filename, index=False, engine='openpyxl')
print(f'✅ تم إنشاء ملف Excel تجريبي: {filename}')
print(f'📁 المسار: {os.path.abspath(filename)}')
print('📊 البيانات:')
print(df.to_string(index=False))
