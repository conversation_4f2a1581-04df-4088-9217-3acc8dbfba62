import os
import logging
import pytz
import sys
from flask import Flask
from flask_login import LoginManager
from flask_migrate import Migrate
from flask_wtf.csrf import CSRFProtect
from db import db
from datetime import datetime, timedelta

# استيراد دالة التوقيت السعودي
from datetime_utils import get_saudi_now

# Set up logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('app.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Initialize login manager
login_manager = LoginManager()

# Initialize CSRF protection
csrf = CSRFProtect()

# Create the Flask app
def create_app():
    app = Flask(__name__)

    # Load configuration
    app.config.from_object('config.Config')

    # إضافة إعدادات المنطقة الزمنية
    app.config['TIMEZONE'] = 'Asia/Riyadh'  # توقيت السعودية (UTC+3)

    # Secret key for sessions
    app.secret_key = os.environ.get("SESSION_SECRET", "military_warehouse_secret")

    # إعدادات الكوكيز لتعزيز الأمان
    app.config['SESSION_COOKIE_SECURE'] = False  # تعطيل HTTPS للتطوير المحلي
    app.config['SESSION_COOKIE_HTTPONLY'] = True  # منع الوصول من JavaScript
    app.config['SESSION_COOKIE_SAMESITE'] = 'Lax'  # حماية من هجمات CSRF

    # إعدادات تذكر المستخدم
    app.config['REMEMBER_COOKIE_DURATION'] = timedelta(days=1)  # مدة تذكر المستخدم - يوم واحد
    app.config['REMEMBER_COOKIE_SECURE'] = False  # تعطيل HTTPS للتطوير المحلي
    app.config['REMEMBER_COOKIE_HTTPONLY'] = True  # منع الوصول من JavaScript
    app.config['REMEMBER_COOKIE_REFRESH_EACH_REQUEST'] = True  # تحديث الكوكي مع كل طلب
    app.config['SESSION_PERMANENT'] = False  # جلسة غير دائمة (تنتهي عند إغلاق المتصفح) إذا لم يتم اختيار "تذكرني"

    # Database configuration
    app.config["SQLALCHEMY_DATABASE_URI"] = os.environ.get("DATABASE_URL", "postgresql://postgres:postgres@localhost:5432/military_warehouse")
    app.config["SQLALCHEMY_ENGINE_OPTIONS"] = {
        "pool_recycle": 300,
        "pool_pre_ping": True,
    }
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

    # Initialize extensions with the app
    db.init_app(app)
    login_manager.init_app(app)
    # csrf.init_app(app)  # تعطيل CSRF للتطوير

    # Set up login manager configuration
    login_manager.login_view = 'auth.login'
    login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة'
    login_manager.login_message_category = 'warning'

    # Import models to ensure they're registered with SQLAlchemy
    with app.app_context():
        import models

        # Set up user loader for Flask-Login
        @login_manager.user_loader
        def load_user(user_id):
            return models.User.query.get(int(user_id))

        # Create database tables
        db.create_all()

        # Initialize admin user and default warehouses
        from auth import init_admin
        init_admin()

    # Register blueprints
    from auth import auth_bp
    from warehouse import warehouse_bp
    from weapons import weapons_bp
    from personnel import personnel_bp
    from inventory import inventory_bp
    from inventory_management import inventory_bp as inventory_management_bp
    from standalone_inventory import standalone_inventory_bp
    from reports import reports_bp
    from statuses import statuses_bp
    from activities import activities_bp
    from receipts import receipts_bp
    from locations import locations_bp




    app.register_blueprint(auth_bp)
    app.register_blueprint(warehouse_bp)
    app.register_blueprint(weapons_bp)
    app.register_blueprint(personnel_bp)
    app.register_blueprint(inventory_bp)
    app.register_blueprint(inventory_management_bp)
    app.register_blueprint(standalone_inventory_bp)
    app.register_blueprint(reports_bp)
    app.register_blueprint(statuses_bp)
    app.register_blueprint(activities_bp)


    app.register_blueprint(receipts_bp)
    app.register_blueprint(locations_bp)


    # Add CSRF token context processor
    @app.context_processor
    def inject_csrf_token():
        """توفير csrf_token للقوالب"""
        def csrf_token():
            # Since CSRF is disabled, return a dummy token to avoid template errors
            return "dummy_csrf_token"
        return dict(csrf_token=csrf_token)

    # Add audit alerts context processor
    @app.context_processor
    def inject_audit_alerts():
        """توفير عدد تنبيهات الجرد للقوالب"""
        def get_audit_alerts_count():
            try:
                from flask_login import current_user
                from models import Audit
                from datetime import datetime, timezone
                from sqlalchemy import and_

                if not current_user.is_authenticated:
                    return 0

                # الحصول على الجرد المكتمل للمستودعات التي يمكن للمستخدم الوصول إليها
                if current_user.is_admin_role:
                    completed_audits = Audit.query.filter_by(status='completed').all()
                else:
                    warehouse_ids = [w.id for w in current_user.warehouses]
                    completed_audits = Audit.query.filter(
                        and_(Audit.status == 'completed', Audit.warehouse_id.in_(warehouse_ids))
                    ).all()

                current_utc = datetime.now(timezone.utc)
                alert_count = 0

                for audit in completed_audits:
                    if audit.next_audit_date:
                        days_remaining = (audit.next_audit_date - current_utc).days
                        # عد التنبيهات للجرد خلال 7 أيام أو المتأخر
                        if days_remaining <= 7:
                            alert_count += 1

                return alert_count
            except:
                return 0

        return dict(get_audit_alerts_count=get_audit_alerts_count)

    # Register custom filters
    from utils import status_color, format_saudi_time
    app.jinja_env.filters['status_color'] = status_color
    app.jinja_env.filters['format_saudi_time'] = format_saudi_time

    # Filtro para convertir saltos de línea en etiquetas <br>
    import jinja2
    def nl2br(value):
        if value:
            return jinja2.utils.markupsafe.Markup(value.replace('\n', '<br>\n'))
        return ""
    app.jinja_env.filters['nl2br'] = nl2br

    # Register permission helpers for templates
    from auth_utils import can_view, can_add, can_edit, can_delete, check_warehouse_access
    app.jinja_env.globals['can_view'] = can_view
    app.jinja_env.globals['can_add'] = can_add
    app.jinja_env.globals['can_edit'] = can_edit
    app.jinja_env.globals['can_delete'] = can_delete
    app.jinja_env.globals['check_warehouse_access'] = check_warehouse_access

    # إضافة دالة وسيطة لمنع التخزين المؤقت للصفحات
    @app.after_request
    def add_security_headers(response):
        # منع التخزين المؤقت للصفحات
        response.headers['Cache-Control'] = 'no-store, no-cache, must-revalidate, max-age=0'
        response.headers['Pragma'] = 'no-cache'
        response.headers['Expires'] = '0'
        # إضافة رؤوس أمان إضافية
        response.headers['X-Content-Type-Options'] = 'nosniff'
        response.headers['X-Frame-Options'] = 'SAMEORIGIN'
        response.headers['X-XSS-Protection'] = '1; mode=block'
        return response

    # Create database migration
    Migrate(app, db)

    return app

# Initialize the app
app = create_app()

# Test route for duties without authentication
@app.route('/test-duties')
def test_duties():
    from flask import render_template
    return render_template('test_duties.html')

# Test route for duties features
@app.route('/test-duties-features')
def test_duties_features():
    from flask import render_template
    return render_template('test_duties_features.html')

# Test route for hijri date API
@app.route('/test-hijri-api')
def test_hijri_api():
    from flask import send_from_directory
    return send_from_directory('.', 'test_hijri_api.html')

# Global API endpoint for locations
@app.route('/api/locations')
def api_locations():
    """Global API endpoint للمواقع"""
    from flask import jsonify
    from models import Location
    try:
        locations = Location.query.filter_by(status='نشط').order_by(Location.name).all()

        locations_data = []
        for location in locations:
            locations_data.append({
                'id': location.id,
                'name': location.name,
                'serial_number': location.serial_number,
                'type': location.type,
                'description': location.description
            })

        return jsonify({
            'success': True,
            'locations': locations_data
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
